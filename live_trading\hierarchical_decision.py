"""
Hierarchical Decision Framework for Specialized Ensemble Architecture

Implements 4-level decision process with veto power at each level:
Level 1: Safety Check (Linear Stability Monitor)
Level 2: Market Context Analysis (CatBoost Market Regime Analyst)  
Level 3: Signal Quality Assessment (LightGBM Signal Generator)
Level 4: Final Decision Integration (XGBoost Risk Manager)
"""

from typing import Dict, Any, Optional, Tuple
from dataclasses import dataclass
from datetime import datetime
import numpy as np
import pandas as pd
try:
    from utils.logging_utils import LoggerMixin
except ImportError:
    # Fallback for missing utils
    import logging
    class LoggerMixin:
        def __init__(self):
            self.logger = logging.getLogger(self.__class__.__name__)

# ENHANCED: Import shared risk context for component communication
try:
    from .shared_risk_context import (
        get_shared_risk_context, RiskAssessment, RiskLevel, RiskSource
    )
except ImportError:
    # Fallback if shared risk context not available
    get_shared_risk_context = None
    RiskAssessment = None
    RiskLevel = None
    RiskSource = None


@dataclass
class DecisionLevel:
    """Represents a decision level in the hierarchy."""
    level: int
    name: str
    model_type: str
    weight: float
    veto_power: bool
    decision: Optional[str] = None
    confidence: float = 0.0
    reasoning: str = ""
    veto_reason: Optional[str] = None


@dataclass
class HierarchicalDecision:
    """Complete hierarchical decision result."""
    final_decision: str  # 'BUY', 'SELL', 'HOLD', 'VETO'
    overall_confidence: float
    decision_levels: Dict[int, DecisionLevel]
    consensus_score: float
    risk_score: float
    entry_timing: str  # 'IMMEDIATE', 'PATIENT', 'WAIT'
    position_sizing: float
    tp_sl_levels: Dict[str, float]
    timestamp: datetime


class HierarchicalDecisionFramework(LoggerMixin):
    """
    Implements the 4-level hierarchical decision framework for specialized ensemble.
    
    Each level has veto power over subsequent levels, ensuring comprehensive
    risk management and decision quality control.
    """
    
    def __init__(self, config: Dict[str, Any]):
        super().__init__()
        # Override logger name to match other components
        import logging
        self.logger = logging.getLogger("HierarchicalDecision")
        self.config = config

        # ENHANCED: Initialize shared risk context for component communication
        self.shared_risk_context = None
        if get_shared_risk_context is not None:
            try:
                self.shared_risk_context = get_shared_risk_context()
                self.logger.info("🔗 Shared risk context initialized for hierarchical decision framework")
            except Exception as e:
                self.logger.warning(f"Failed to initialize shared risk context: {e}")
                self.shared_risk_context = None
        else:
            self.logger.info("📝 Shared risk context not available - operating in standalone mode")
        
        # Decision level configuration - FIXED: Re-enabled safety check with proper weights
        self.decision_levels = {
            1: DecisionLevel(
                level=1,
                name="Safety Check",
                model_type="linear",
                weight=0.10,  # 🔧 FIXED: Re-enabled with 10% weight for safety oversight
                veto_power=True  # 🔧 FIXED: Re-enabled veto power for critical safety issues
            ),
            2: DecisionLevel(
                level=2,
                name="Market Context Analysis",
                model_type="catboost",
                weight=0.25,
                veto_power=True
            ),
            3: DecisionLevel(
                level=3,
                name="Signal Quality Assessment",
                model_type="lightgbm",
                weight=0.40,
                veto_power=True
            ),
            4: DecisionLevel(
                level=4,
                name="Final Decision Integration",
                model_type="xgboost",
                weight=0.25,
                veto_power=False  # Final level doesn't veto, just adjusts
            )
        }
        
        # Thresholds from config - adjusted for realistic trading
        self.min_confidence_threshold = config.get('min_signal_confidence', 0.50)  # Lowered from 0.65
        self.high_confidence_threshold = config.get('high_confidence_threshold', 0.75)  # Lowered from 0.8
        self.consensus_threshold = config.get('ensemble_consensus_threshold', 0.60)  # Lowered from 0.7

        # 🔧 FIXED: Market regime protection thresholds (more realistic for live trading)
        self.regime_protection_config = {
            'unfavorable_regime_threshold': config.get('unfavorable_regime_threshold', 0.25),  # Lowered from 0.3 to 0.25
            'min_regime_confidence': {
                'asian_session': config.get('asian_regime_confidence_min', 0.35),      # Lowered from 0.45
                'european_session': config.get('european_regime_confidence_min', 0.30), # Lowered from 0.40
                'us_session': config.get('us_regime_confidence_min', 0.25)             # Lowered from 0.35
            },
            'extreme_volatility_regime_confidence_min': config.get('extreme_vol_regime_confidence_min', 0.55), # Lowered from 0.65
            'regime_multipliers': {
                'RANGING': config.get('ranging_regime_multiplier', 0.6),    # Raised from 0.5
                'TRENDING': config.get('trending_regime_multiplier', 0.8),  # Unchanged
                'VOLATILE': config.get('volatile_regime_multiplier', 0.4),  # 🔧 CRITICAL FIX: Raised from 0.2 to 0.4
                'UNCERTAIN': config.get('uncertain_regime_multiplier', 0.15) # Raised from 0.1
            },
            'session_multipliers': {
                'asian': config.get('asian_session_multiplier', 0.9),
                'european': config.get('european_session_multiplier', 1.0),
                'us': config.get('us_session_multiplier', 1.2),
                'overlap': config.get('overlap_session_multiplier', 1.1)
            }
        }
        
        # Decision history for learning
        self.decision_history = []
        
        self.logger.info("Hierarchical Decision Framework initialized")
    
    def make_decision(self, model_predictions: Dict[str, Any],
                     market_context: Dict[str, Any]) -> HierarchicalDecision:
        """
        Make hierarchical decision through 4-level process.

        UPDATED: Enhanced to handle both individual model predictions and ensemble predictions.

        Args:
            model_predictions: Predictions from specialized models or ensemble
            market_context: Current market context and conditions

        Returns:
            HierarchicalDecision with complete decision analysis
        """
        # Professional logging for hierarchical decision analysis
        self.logger.info("🚀 HIERARCHICAL DECISION ANALYSIS INITIATED")
        self.logger.info(f"   Model predictions: {len(model_predictions)} models")
        self.logger.info(f"   Market context: {market_context.get('current_price', 'N/A')} | Regime: {market_context.get('market_regime', 'N/A')}")

        try:
            timestamp = datetime.now()
            decision_levels = {}

            # UPDATED: Enhanced prediction format detection and handling
            self.logger.info("🔍 HIERARCHICAL DECISION ANALYSIS STARTING")
            self.logger.info("=" * 60)

            # UPDATED: Detect if we have ensemble predictions or individual model predictions
            ensemble_predictions = self._detect_ensemble_predictions(model_predictions)

            if ensemble_predictions:
                self.logger.info("🤖 DETECTED ENSEMBLE PREDICTIONS - Using ensemble-aware decision process")
                return self._make_ensemble_aware_decision(model_predictions, market_context, timestamp)
            else:
                self.logger.info("🔧 DETECTED INDIVIDUAL MODEL PREDICTIONS - Using traditional decision process")
                return self._make_traditional_decision(model_predictions, market_context, timestamp)

        except Exception as e:
            self.logger.error(f"🚨 HIERARCHICAL DECISION EXCEPTION: {str(e)}")
            import traceback
            self.logger.error(f"🚨 HIERARCHICAL DECISION TRACEBACK: {traceback.format_exc()}")
            return self._create_error_decision(str(e), timestamp)

    def _detect_ensemble_predictions(self, model_predictions: Dict[str, Any]) -> bool:
        """
        Detect if the predictions are from an ensemble model or individual models.

        NEW: Detection method for ensemble vs individual predictions.
        """
        try:
            # Check for ensemble prediction indicators
            ensemble_indicators = [
                'ensemble',
                'predictions',
                'analysis',
                'trading_signal',
                'metadata'
            ]

            # If we have a single prediction with ensemble structure
            if len(model_predictions) == 1:
                prediction = list(model_predictions.values())[0]
                if isinstance(prediction, dict):
                    has_ensemble_structure = any(key in prediction for key in ensemble_indicators)
                    if has_ensemble_structure:
                        self.logger.info("✅ Detected ensemble prediction structure")
                        return True

            # Check for LiveModelEngine prediction format
            for model_name, prediction in model_predictions.items():
                if isinstance(prediction, dict):
                    if 'predictions' in prediction and 'analysis' in prediction and 'trading_signal' in prediction:
                        self.logger.info(f"✅ Detected LiveModelEngine ensemble format in {model_name}")
                        return True

            return False

        except Exception as e:
            self.logger.warning(f"⚠️ Failed to detect prediction format: {str(e)}")
            return False

    def _make_ensemble_aware_decision(self, model_predictions: Dict[str, Any],
                                    market_context: Dict[str, Any],
                                    timestamp: datetime) -> HierarchicalDecision:
        """
        Make decision using ensemble predictions.

        NEW: Ensemble-aware decision making process.
        """
        try:
            self.logger.info("🤖 ENSEMBLE-AWARE DECISION PROCESS")

            # Extract ensemble prediction data
            ensemble_data = self._extract_ensemble_data(model_predictions)

            # Log ensemble data
            self._log_ensemble_data(ensemble_data)

            # Create decision levels based on ensemble data
            decision_levels = self._create_ensemble_decision_levels(ensemble_data, market_context)

            # Create final hierarchical decision
            final_decision = self._integrate_ensemble_decisions(decision_levels, ensemble_data, market_context)
            final_decision.timestamp = timestamp

            # Store in history
            self.decision_history.append(final_decision)

            self.logger.info(f"🎯 ENSEMBLE HIERARCHICAL DECISION: {final_decision.final_decision} "
                           f"(confidence: {final_decision.overall_confidence:.3f}, "
                           f"consensus: {final_decision.consensus_score:.3f})")

            return final_decision

        except Exception as e:
            self.logger.error(f"🚨 ENSEMBLE DECISION FAILED: {str(e)}")
            return self._create_error_decision(str(e), timestamp)

    def _make_traditional_decision(self, model_predictions: Dict[str, Any],
                                 market_context: Dict[str, Any],
                                 timestamp: datetime) -> HierarchicalDecision:
        """
        Make decision using traditional individual model predictions.

        UPDATED: Traditional decision process with enhanced logging.
        """
        try:
            self.logger.info("🔧 TRADITIONAL DECISION PROCESS")
            decision_levels = {}

            # Log individual model outputs with key metrics
            for model_name, prediction in model_predictions.items():
                if isinstance(prediction, dict):
                    confidence = prediction.get('confidence', 0.0)
                    outputs = prediction.get('outputs', {})

                    # Also check for direct prediction values (from LiveModelEngine)
                    predictions_dict = prediction.get('predictions', {})
                    trading_signal = prediction.get('trading_signal', {})

                    key_metrics = []

                    # Extract key metrics from outputs
                    if 'signal_strength' in outputs:
                        key_metrics.append(f"signal_strength={outputs['signal_strength']:.3f}")
                    if 'confidence_score' in outputs:
                        key_metrics.append(f"confidence_score={outputs['confidence_score']:.3f}")
                    if 'direction_classification' in outputs:
                        key_metrics.append(f"direction={outputs['direction_classification']}")
                    if 'signal_probability' in outputs:
                        key_metrics.append(f"signal_prob={outputs['signal_probability']:.3f}")
                    if 'risk_classification' in outputs:
                        key_metrics.append(f"risk={outputs['risk_classification']}")
                    if 'market_regime' in outputs:
                        key_metrics.append(f"regime={outputs['market_regime']}")
                    if 'session_favorability' in outputs:
                        key_metrics.append(f"session={outputs['session_favorability']}")
                    if 'volatility_state' in outputs:
                        key_metrics.append(f"volatility={outputs['volatility_state']}")
                    if 'baseline_signal' in outputs:
                        key_metrics.append(f"baseline={outputs['baseline_signal']}")
                    if 'system_health_score' in outputs:
                        key_metrics.append(f"health={outputs['system_health_score']:.3f}")

                    # Extract key metrics from predictions (LiveModelEngine format)
                    if 'signal_probability' in predictions_dict:
                        key_metrics.append(f"signal_prob={predictions_dict['signal_probability']:.3f}")
                    if 'tp1_distance' in predictions_dict:
                        key_metrics.append(f"tp1={predictions_dict['tp1_distance']:.1f}")
                    if 'tp2_distance' in predictions_dict:
                        key_metrics.append(f"tp2={predictions_dict['tp2_distance']:.1f}")
                    if 'sl_distance' in predictions_dict:
                        key_metrics.append(f"sl={predictions_dict['sl_distance']:.1f}")
                    if 'volatility_adjustment' in predictions_dict:
                        key_metrics.append(f"vol_adj={predictions_dict['volatility_adjustment']:.2f}")
                    if 'market_regime' in predictions_dict:
                        key_metrics.append(f"regime={predictions_dict['market_regime']:.3f}")

                    # Extract from trading signal
                    if 'direction' in trading_signal:
                        key_metrics.append(f"direction={trading_signal['direction']}")
                    if 'confidence' in trading_signal:
                        key_metrics.append(f"signal_conf={trading_signal['confidence']:.3f}")

                    metrics_str = ", ".join(key_metrics) if key_metrics else "no key metrics"
                    self.logger.info(f"📊 {model_name.upper()}: confidence={confidence:.3f} | {metrics_str}")

                    # Log the full prediction structure for debugging
                    if predictions_dict:
                        self.logger.info(f"   🔧 Full predictions: {predictions_dict}")
                    if trading_signal:
                        self.logger.info(f"   🎯 Trading signal: {trading_signal}")

                else:
                    self.logger.info(f"📊 {model_name.upper()}: {prediction}")

            self.logger.info("=" * 60)

            # Level 1: Safety Check (Linear Stability Monitor)
            self.logger.info("🛡️  LEVEL 1: SAFETY CHECK (Linear Stability Monitor)")
            level1_result = self._level1_safety_check(
                model_predictions.get('linear', {}),
                market_context
            )
            decision_levels[1] = level1_result

            self.logger.info(f"   Result: {level1_result.decision} | Confidence: {level1_result.confidence:.3f}")
            if level1_result.reasoning:
                self.logger.info(f"   Reasoning: {level1_result.reasoning}")

            if level1_result.veto_reason:
                self.logger.info(f"❌ LEVEL 1 VETO: {level1_result.veto_reason}")
                return self._create_veto_decision(level1_result, decision_levels, timestamp)

            # Level 2: Market Context Analysis (CatBoost Market Regime Analyst)
            self.logger.info("🌍 LEVEL 2: MARKET CONTEXT ANALYSIS (CatBoost Market Regime Analyst)")
            level2_result = self._level2_market_context(
                model_predictions.get('catboost', {}),
                market_context,
                level1_result
            )
            decision_levels[2] = level2_result

            self.logger.info(f"   Result: {level2_result.decision} | Confidence: {level2_result.confidence:.3f}")
            if level2_result.reasoning:
                self.logger.info(f"   Reasoning: {level2_result.reasoning}")

            if level2_result.veto_reason:
                self.logger.info(f"❌ LEVEL 2 VETO: {level2_result.veto_reason}")
                return self._create_veto_decision(level2_result, decision_levels, timestamp)

            # Level 3: Signal Quality Assessment (LightGBM Signal Generator)
            self.logger.info("📡 LEVEL 3: SIGNAL QUALITY ASSESSMENT (LightGBM Signal Generator)")
            level3_result = self._level3_signal_quality(
                model_predictions.get('lightgbm', {}),
                market_context,
                level1_result,
                level2_result
            )
            decision_levels[3] = level3_result

            self.logger.info(f"   Result: {level3_result.decision} | Confidence: {level3_result.confidence:.3f}")
            if level3_result.reasoning:
                self.logger.info(f"   Reasoning: {level3_result.reasoning}")

            if level3_result.veto_reason:
                self.logger.info(f"❌ LEVEL 3 VETO: {level3_result.veto_reason}")
                return self._create_veto_decision(level3_result, decision_levels, timestamp)
            
            # Level 4: Final Decision Integration (XGBoost Risk Manager)
            self.logger.info("⚖️  LEVEL 4: FINAL DECISION INTEGRATION (XGBoost Risk Manager)")
            level4_result = self._level4_final_integration(
                model_predictions.get('xgboost', {}),
                market_context,
                level1_result,
                level2_result,
                level3_result
            )
            decision_levels[4] = level4_result

            self.logger.info(f"   Result: {level4_result.decision} | Confidence: {level4_result.confidence:.3f}")
            if level4_result.reasoning:
                self.logger.info(f"   Reasoning: {level4_result.reasoning}")
            
            # Create final hierarchical decision
            final_decision = self._integrate_decisions(decision_levels, model_predictions, market_context)
            final_decision.timestamp = timestamp
            
            # Store in history
            self.decision_history.append(final_decision)
            
            self.logger.info(f"Hierarchical decision: {final_decision.final_decision} "
                           f"(confidence: {final_decision.overall_confidence:.3f}, "
                           f"consensus: {final_decision.consensus_score:.3f})")
            
            return final_decision
            
        except Exception as e:
            self.logger.error(f"🚨 HIERARCHICAL DECISION EXCEPTION: {str(e)}")
            import traceback
            self.logger.error(f"🚨 HIERARCHICAL DECISION TRACEBACK: {traceback.format_exc()}")
            return self._create_error_decision(str(e), timestamp)
    
    def _level1_safety_check(self, linear_pred: Dict[str, Any],
                           market_context: Dict[str, Any]) -> DecisionLevel:
        """
        Level 1: Safety Check using Linear Stability Monitor.

        FIXED: Properly checks for emergency conditions, system health, and basic risk limits.
        """
        level = DecisionLevel(
            level=1,
            name="Safety Check",
            model_type="linear",
            weight=0.10,  # 🔧 FIXED: Re-enabled with proper weight
            veto_power=True  # 🔧 FIXED: Re-enabled veto power for safety
        )

        try:
            # 🔧 FIXED: Extract linear model outputs with proper field name mapping
            if isinstance(linear_pred, dict) and 'outputs' in linear_pred:
                outputs = linear_pred['outputs']

                # Map actual linear model output fields to expected fields
                signal_prob = outputs.get('signal_probability', 0.5)
                volatility_adj = outputs.get('volatility_adjustment', 1.0)

                # 🔧 CRITICAL FIX: Use actual field names from linear model
                stability_score = outputs.get('overall_stability_score', outputs.get('stability_score', 0.5))
                system_health = outputs.get('system_health_score', 1.0)

                # Map system health to risk classification if not provided
                if 'risk_classification' in outputs:
                    risk_level = outputs['risk_classification']
                else:
                    # Derive risk level from system health and market stress
                    market_stress = outputs.get('market_stress_indicator', 0.0)
                    if system_health >= 0.8 and market_stress <= 0.2:
                        risk_level = 'LOW'
                    elif system_health >= 0.6 and market_stress <= 0.5:
                        risk_level = 'MEDIUM'
                    elif system_health >= 0.4 and market_stress <= 0.8:
                        risk_level = 'HIGH'
                    else:
                        risk_level = 'EXTREME'

            else:
                # Fallback for direct dictionary access
                signal_prob = linear_pred.get('signal_probability', 0.5)
                volatility_adj = linear_pred.get('volatility_adjustment', 1.0)
                stability_score = linear_pred.get('overall_stability_score', linear_pred.get('stability_score', 0.5))
                system_health = linear_pred.get('system_health_score', 1.0)
                risk_level = 'MEDIUM'

            # 🔧 ENHANCED LOGGING: Show actual vs expected field mappings
            self.logger.info(f"     🔍 LINEAR MODEL FIELD MAPPING:")
            self.logger.info(f"       Signal Probability: {signal_prob:.3f}")
            self.logger.info(f"       Volatility Adjustment: {volatility_adj:.3f}")
            self.logger.info(f"       Stability Score: {stability_score:.3f} (mapped from overall_stability_score)")
            self.logger.info(f"       System Health: {system_health:.3f}")
            self.logger.info(f"       Risk Level: {risk_level} (derived)")

            # 🔧 FIXED: Always perform safety checks (removed the veto_power condition)
            veto_reasons = []

            # Check extreme volatility - FIXED: More reasonable threshold
            if volatility_adj > 3.0:  # Raised from 2.5 to 3.0 for less restrictive
                veto_reasons.append(f"Extreme volatility detected (adj: {volatility_adj:.2f})")

            # 🔧 FIXED: Improved market hours logic - avoid only truly dead hours
            import pytz
            current_utc_hour = datetime.now(pytz.UTC).hour
            # Only veto during weekend gap (Friday 22:00 UTC to Sunday 22:00 UTC is market closed)
            # But since we're trading 24/5, only avoid the absolute dead zone
            current_utc = datetime.now(pytz.UTC)
            weekday = current_utc.weekday()  # 0=Monday, 6=Sunday

            # Veto only during weekend (Saturday and Sunday before 22:00 UTC)
            if weekday == 5:  # Saturday
                veto_reasons.append("Weekend - market closed (Saturday)")
            elif weekday == 6 and current_utc_hour < 22:  # Sunday before 22:00 UTC
                veto_reasons.append("Weekend - market closed (Sunday before 22:00 UTC)")

            # Check system health indicators
            if market_context.get('data_quality_score', 1.0) < 0.7:  # Lowered from 0.8 to 0.7
                veto_reasons.append(f"Poor data quality ({market_context.get('data_quality_score', 1.0):.2f})")

            # Check for emergency stops
            if market_context.get('emergency_stop_triggered', False):
                veto_reasons.append("Emergency stop active")

            # 🔧 CRITICAL FIX: Use system health as primary stability indicator
            if system_health < 0.3:  # Very poor system health
                veto_reasons.append(f"Critical system health issue (health: {system_health:.3f})")

            # 🔧 FIXED: Calculate proper confidence based on actual model outputs
            # Use the higher of stability_score and system_health as base confidence
            base_confidence = max(stability_score, system_health)

            # Adjust confidence based on volatility (higher vol = lower confidence)
            vol_confidence_adj = max(0.1, 1.0 - (volatility_adj - 1.0) * 0.2)

            # Adjust confidence based on risk level
            risk_multipliers = {'LOW': 1.1, 'MEDIUM': 1.0, 'HIGH': 0.8, 'EXTREME': 0.6}
            risk_multiplier = risk_multipliers.get(risk_level, 1.0)

            # Calculate final confidence
            final_confidence = base_confidence * vol_confidence_adj * risk_multiplier
            final_confidence = max(0.0, min(1.0, final_confidence))  # Clamp to [0,1]

            # 🔧 ENHANCED LOGGING: Show confidence calculation breakdown
            self.logger.info(f"     🧮 CONFIDENCE CALCULATION BREAKDOWN:")
            self.logger.info(f"       Base Confidence: {base_confidence:.3f} (max of stability: {stability_score:.3f}, health: {system_health:.3f})")
            self.logger.info(f"       Volatility Adjustment: {vol_confidence_adj:.3f} (vol_adj: {volatility_adj:.3f})")
            self.logger.info(f"       Risk Multiplier: {risk_multiplier:.3f} (risk: {risk_level})")
            self.logger.info(f"       Final Confidence: {final_confidence:.3f}")

            # Determine decision
            if veto_reasons:
                level.decision = "VETO"
                level.veto_reason = "; ".join(veto_reasons)
                level.confidence = 0.0  # 🔧 FIXED: Set to 0 when vetoed
                level.reasoning = f"Safety veto: {level.veto_reason}"
            else:
                level.decision = "PROCEED"
                level.confidence = final_confidence  # 🔧 FIXED: Use calculated confidence
                level.reasoning = f"Safety check passed (vol_adj: {volatility_adj:.2f}, confidence: {final_confidence:.3f})"

            return level

        except Exception as e:
            level.decision = "VETO"
            level.veto_reason = f"Safety check error: {str(e)}"
            level.confidence = 0.0
            level.reasoning = level.veto_reason
            return level

    def _level2_market_context(self, catboost_pred: Dict[str, Any],
                              market_context: Dict[str, Any],
                              level1: DecisionLevel) -> DecisionLevel:
        """
        Level 2: Market Context Analysis using CatBoost Market Regime Analyst.

        Analyzes market regime, volatility conditions, and trading session context.
        """
        level = DecisionLevel(
            level=2,
            name="Market Context Analysis",
            model_type="catboost",
            weight=0.25,
            veto_power=True
        )

        try:
            # 🔧 FIXED: Extract CatBoost regime analysis with corrected regime mappings
            # Handle both direct dictionary access and SpecializedOutput structure
            if isinstance(catboost_pred, dict) and 'outputs' in catboost_pred:
                # SpecializedOutput structure from specialized ensemble
                catboost_outputs = catboost_pred['outputs']
                market_regime_str = catboost_outputs.get('market_regime', 'RANGING')
                # Use main CatBoost confidence for decision making, not internal regime_confidence
                regime_confidence = catboost_pred.get('confidence', 0.5)

                # 🔧 CRITICAL FIX: More realistic regime mapping for live trading
                regime_mapping = {
                    'RANGING': 0.6,      # Slightly favorable - predictable patterns
                    'TRENDING': 0.8,     # Favorable regime - clear direction
                    'VOLATILE': 0.4,     # 🔧 FIXED: Raised from 0.2 to 0.4 - volatile but tradeable
                    'UNCERTAIN': 0.15    # Unfavorable regime - unpredictable
                }
                market_regime = regime_mapping.get(market_regime_str, 0.5)

                self.logger.info(f"   🔍 CatBoost Regime Analysis (FIXED MAPPING):")
                self.logger.info(f"      Market Regime: {market_regime_str} → {market_regime:.2f} (UPDATED)")
                self.logger.info(f"      Main CatBoost Confidence: {regime_confidence:.3f} (used for decisions)")
                self.logger.info(f"      Internal Regime Confidence: {catboost_outputs.get('regime_confidence', 0.5):.3f} (stability measure)")

            else:
                # Fallback for direct dictionary access (legacy support)
                market_regime = catboost_pred.get('market_regime', 0.5)
                regime_confidence = catboost_pred.get('regime_confidence', 0.5)

                # If market_regime is still a string, convert it with updated mapping
                if isinstance(market_regime, str):
                    regime_mapping = {
                        'RANGING': 0.6, 'TRENDING': 0.8, 'VOLATILE': 0.4, 'UNCERTAIN': 0.15  # 🔧 FIXED mappings
                    }
                    market_regime = regime_mapping.get(market_regime, 0.5)

                self.logger.info(f"   🔍 CatBoost Regime Analysis (Legacy - FIXED MAPPING):")
                self.logger.info(f"      Market Regime: {market_regime:.2f} (UPDATED)")
                self.logger.info(f"      Regime Confidence: {regime_confidence:.3f}")

            # Market context checks
            veto_reasons = []

            # ENHANCED: Comprehensive market regime suitability check
            self.logger.info(f"   🌍 COMPREHENSIVE MARKET REGIME ANALYSIS:")
            self.logger.info(f"      Market Regime Value: {market_regime:.3f}")
            self.logger.info(f"      Regime Confidence: {regime_confidence:.3f}")
            self.logger.info(f"      Level 1 Confidence: {level1.confidence:.3f}")
            self.logger.info(f"      Unfavorable Threshold: {self.regime_protection_config['unfavorable_regime_threshold']:.3f}")

            # 🔧 FIXED: Primary regime check with more reasonable requirements
            unfavorable_threshold = self.regime_protection_config['unfavorable_regime_threshold']
            if market_regime < unfavorable_threshold:
                self.logger.warning(f"      ⚠️  UNFAVORABLE MARKET REGIME: {market_regime:.3f} < {unfavorable_threshold:.3f}")

                # 🔧 FIXED: More reasonable safety confidence requirement calculation
                # Base requirement lowered from 0.7 to 0.5, multiplier reduced from 0.5 to 0.3
                required_safety_confidence = 0.5 + (unfavorable_threshold - market_regime) * 0.3
                required_safety_confidence = min(0.85, required_safety_confidence)  # Cap lowered from 95% to 85%

                self.logger.info(f"      Required Safety Confidence: {required_safety_confidence:.3f} (UPDATED CALCULATION)")

                if level1.confidence < required_safety_confidence:
                    veto_reason = f"Unfavorable market regime ({market_regime:.3f}) requires safety confidence ≥{required_safety_confidence:.3f}, got {level1.confidence:.3f}"
                    veto_reasons.append(veto_reason)
                    self.logger.warning(f"      ❌ VETO: {veto_reason}")
                else:
                    self.logger.info(f"      ✅ Proceeding despite unfavorable regime due to sufficient safety confidence ({level1.confidence:.3f})")
            else:
                self.logger.info(f"      ✅ Market regime acceptable: {market_regime:.3f} ≥ {unfavorable_threshold:.3f}")

            # Additional regime confidence check
            if regime_confidence < 0.3:  # Very low regime confidence
                veto_reason = f"Extremely low regime confidence ({regime_confidence:.3f}) indicates market uncertainty"
                veto_reasons.append(veto_reason)
                self.logger.warning(f"      ❌ VETO: {veto_reason}")

            # Check session-based trading rules (use UTC time for proper session detection)
            import pytz
            from datetime import datetime as dt  # Import with alias to avoid conflict
            current_utc = dt.now(pytz.UTC)
            current_local = dt.now()
            current_hour = current_utc.hour
            session_multiplier = 1.0

            # 🔧 ENHANCED DEBUG LOGGING - Always log time comparison
            print(f"🕐 DEBUG TIME COMPARISON:")
            print(f"   Local time: {current_local.strftime('%Y-%m-%d %H:%M:%S')}")
            print(f"   UTC time: {current_utc.strftime('%Y-%m-%d %H:%M:%S')}")
            print(f"   UTC hour: {current_hour}")

            # ENHANCED: Session-based regime confidence requirements with configurable thresholds
            if 0 <= current_hour < 8:  # Asian session (00:00-08:00 UTC)
                current_session = "asian"
                session_multiplier = self.regime_protection_config['session_multipliers']['asian']
                min_regime_confidence = self.regime_protection_config['min_regime_confidence']['asian_session']

                self.logger.info(f"   🌏 ASIAN SESSION ANALYSIS (UTC hour {current_hour}):")
                self.logger.info(f"      Session Multiplier: {session_multiplier:.2f}")
                self.logger.info(f"      Min Regime Confidence Required: {min_regime_confidence:.3f}")
                self.logger.info(f"      Current Regime Confidence: {regime_confidence:.3f}")

                if regime_confidence < min_regime_confidence:
                    veto_reason = f"Asian session requires regime confidence ≥{min_regime_confidence:.3f}, got {regime_confidence:.3f}"
                    veto_reasons.append(veto_reason)
                    self.logger.warning(f"      ❌ VETO: {veto_reason}")
                else:
                    self.logger.info(f"      ✅ Asian session regime confidence acceptable")

            elif 8 <= current_hour < 16:  # European session (08:00-16:00 UTC)
                current_session = "european"
                session_multiplier = self.regime_protection_config['session_multipliers']['european']
                min_regime_confidence = self.regime_protection_config['min_regime_confidence']['european_session']

                self.logger.info(f"   🇪🇺 EUROPEAN SESSION ANALYSIS (UTC hour {current_hour}):")
                self.logger.info(f"      Session Multiplier: {session_multiplier:.2f}")
                self.logger.info(f"      Min Regime Confidence Required: {min_regime_confidence:.3f}")
                self.logger.info(f"      Current Regime Confidence: {regime_confidence:.3f}")

                if regime_confidence < min_regime_confidence:
                    veto_reason = f"European session requires regime confidence ≥{min_regime_confidence:.3f}, got {regime_confidence:.3f}"
                    veto_reasons.append(veto_reason)
                    self.logger.warning(f"      ❌ VETO: {veto_reason}")
                else:
                    self.logger.info(f"      ✅ European session regime confidence acceptable")

            else:  # US session (16:00-24:00 UTC)
                current_session = "us"
                session_multiplier = self.regime_protection_config['session_multipliers']['us']
                min_regime_confidence = self.regime_protection_config['min_regime_confidence']['us_session']

                self.logger.info(f"   🇺🇸 US SESSION ANALYSIS (UTC hour {current_hour}):")
                self.logger.info(f"      Session Multiplier: {session_multiplier:.2f}")
                self.logger.info(f"      Min Regime Confidence Required: {min_regime_confidence:.3f}")
                self.logger.info(f"      Current Regime Confidence: {regime_confidence:.3f}")

                if regime_confidence < min_regime_confidence:
                    veto_reason = f"US session requires regime confidence ≥{min_regime_confidence:.3f}, got {regime_confidence:.3f}"
                    veto_reasons.append(veto_reason)
                    self.logger.warning(f"      ❌ VETO: {veto_reason}")
                else:
                    self.logger.info(f"      ✅ US session regime confidence acceptable")

            # Log current session for debugging
            self.logger.warning(f"🕐 HIERARCHICAL DECISION: UTC={current_utc.strftime('%H:%M:%S')}, Session={current_session}, RegimeConf={regime_confidence}")

            # ENHANCED: Comprehensive volatility regime compatibility check
            vol_regime = market_context.get('volatility_regime', 'normal')
            extreme_vol_min_confidence = self.regime_protection_config['extreme_volatility_regime_confidence_min']

            self.logger.info(f"   🌪️  VOLATILITY REGIME ANALYSIS:")
            self.logger.info(f"      Volatility Regime: {vol_regime}")
            self.logger.info(f"      Current Regime Confidence: {regime_confidence:.3f}")
            self.logger.info(f"      Extreme Vol Min Confidence: {extreme_vol_min_confidence:.3f}")

            # Multi-tier volatility protection
            if vol_regime == 'extreme':
                if regime_confidence < extreme_vol_min_confidence:
                    veto_reason = f"Extreme volatility requires regime confidence ≥{extreme_vol_min_confidence:.3f}, got {regime_confidence:.3f}"
                    veto_reasons.append(veto_reason)
                    self.logger.warning(f"      ❌ VETO: {veto_reason}")
                else:
                    self.logger.info(f"      ⚠️  Extreme volatility accepted due to high regime confidence ({regime_confidence:.3f})")
                    session_multiplier *= 0.8  # Reduce session multiplier for extreme volatility

            elif vol_regime == 'high' and regime_confidence < 0.5:
                veto_reason = f"High volatility with insufficient regime confidence ({regime_confidence:.3f} < 0.5)"
                veto_reasons.append(veto_reason)
                self.logger.warning(f"      ❌ VETO: {veto_reason}")

            elif vol_regime == 'low' and regime_confidence < 0.35:
                veto_reason = f"Low volatility with very poor regime confidence ({regime_confidence:.3f} < 0.35)"
                veto_reasons.append(veto_reason)
                self.logger.warning(f"      ❌ VETO: {veto_reason}")

            else:
                self.logger.info(f"      ✅ Volatility regime acceptable: {vol_regime} with confidence {regime_confidence:.3f}")

            # ENHANCED: Extract additional regime information for better decision making
            session_favorability = "FAIR"  # Default
            volatility_state = "NORMAL"    # Default

            if isinstance(catboost_pred, dict) and 'outputs' in catboost_pred:
                catboost_outputs = catboost_pred['outputs']
                session_favorability = catboost_outputs.get('session_favorability', 'FAIR')
                volatility_state = catboost_outputs.get('volatility_state', 'NORMAL')

                self.logger.info(f"   📊 Additional CatBoost Context:")
                self.logger.info(f"      Session Favorability: {session_favorability}")
                self.logger.info(f"      Volatility State: {volatility_state}")

            # Determine decision with comprehensive logging
            if veto_reasons:
                level.decision = "VETO"
                level.veto_reason = "; ".join(veto_reasons)
                level.confidence = 0.0
                level.reasoning = f"Market context veto: {level.veto_reason}"

                self.logger.warning(f"   ❌ LEVEL 2 DECISION: VETO")
                self.logger.warning(f"      Veto Reasons: {level.veto_reason}")
            else:
                level.decision = "PROCEED"
                level.confidence = regime_confidence * session_multiplier
                level.reasoning = f"Market context favorable (regime: {market_regime:.2f}, session_mult: {session_multiplier:.2f}, session_fav: {session_favorability})"

                self.logger.info(f"   ✅ LEVEL 2 DECISION: PROCEED")
                self.logger.info(f"      Final Confidence: {level.confidence:.3f}")
                self.logger.info(f"      Reasoning: {level.reasoning}")

            # ENHANCED: Update shared risk context with market regime information
            if self.shared_risk_context is not None:
                try:
                    # Update market regime information
                    self.shared_risk_context.update_market_regime(
                        regime=market_regime_str if 'market_regime_str' in locals() else 'UNKNOWN',
                        confidence=regime_confidence,
                        numeric_value=market_regime,
                        source=RiskSource.HIERARCHICAL_DECISION
                    )

                    # Update session information
                    self.shared_risk_context.update_session_info(
                        session=current_session,
                        multiplier=session_multiplier,
                        source=RiskSource.HIERARCHICAL_DECISION
                    )

                    # Update volatility information
                    self.shared_risk_context.update_volatility_info(
                        regime=vol_regime,
                        state=volatility_state,
                        source=RiskSource.HIERARCHICAL_DECISION
                    )

                    # Add risk assessment if there are veto reasons
                    if veto_reasons:
                        from datetime import datetime as dt  # Import with alias
                        risk_assessment = RiskAssessment(
                            source=RiskSource.HIERARCHICAL_DECISION,
                            level=RiskLevel.HIGH if len(veto_reasons) > 2 else RiskLevel.MEDIUM,
                            confidence=1.0 - regime_confidence,  # Higher confidence in risk when regime confidence is low
                            reasoning="; ".join(veto_reasons),
                            timestamp=dt.now()
                        )
                        self.shared_risk_context.add_risk_assessment(risk_assessment)

                except Exception as e:
                    self.logger.warning(f"Failed to update shared risk context: {e}")

            # ENHANCED: Log comprehensive market regime protection summary
            self.logger.info(f"🛡️  MARKET REGIME PROTECTION SUMMARY:")
            self.logger.info(f"   Market Regime: {market_regime_str if 'market_regime_str' in locals() else 'N/A'} → {market_regime:.3f}")
            self.logger.info(f"   Regime Confidence: {regime_confidence:.3f}")
            self.logger.info(f"   Session: {current_session.title()} (multiplier: {session_multiplier:.3f})")
            self.logger.info(f"   Session Favorability: {session_favorability}")
            self.logger.info(f"   Volatility State: {volatility_state}")
            self.logger.info(f"   Volatility Regime: {vol_regime}")
            self.logger.info(f"   Total Veto Reasons: {len(veto_reasons)}")
            self.logger.info(f"   Final Decision: {level.decision}")
            if self.shared_risk_context is not None:
                self.logger.info(f"   Shared Context Updated: ✅")

            return level

        except Exception as e:
            # ENHANCED: Comprehensive error handling with detailed logging
            error_msg = f"Market context analysis error: {str(e)}"
            self.logger.error(error_msg)

            # Log stack trace for debugging
            import traceback
            self.logger.error(f"Market context error traceback: {traceback.format_exc()}")

            # Create safe fallback decision
            level.decision = "VETO"
            level.veto_reason = error_msg
            level.confidence = 0.0
            level.reasoning = f"Error in market context analysis: {str(e)}"

            # Update shared risk context with error condition
            if self.shared_risk_context is not None:
                try:
                    from datetime import datetime
                    error_assessment = RiskAssessment(
                        source=RiskSource.HIERARCHICAL_DECISION,
                        level=RiskLevel.HIGH,
                        confidence=1.0,
                        reasoning=f"Market context analysis error: {str(e)}",
                        timestamp=datetime.now()
                    )
                    self.shared_risk_context.add_risk_assessment(error_assessment)
                except Exception as context_error:
                    self.logger.error(f"Failed to update shared risk context with error: {context_error}")

            return level

    def _level3_signal_quality(self, lightgbm_pred: Dict[str, Any],
                            market_context: Dict[str, Any],
                            level1: DecisionLevel,
                            level2: DecisionLevel) -> DecisionLevel:
        """
        Level 3: Signal Quality Assessment using LightGBM Signal Generator.

        Evaluates signal strength, confidence, and trading opportunity quality.
        """
        level = DecisionLevel(
            level=3,
            name="Signal Quality Assessment",
            model_type="lightgbm",
            weight=0.40,
            veto_power=True
        )

        try:
            # Extract LightGBM signal analysis
            print(f"lightgbm_pred ***: {lightgbm_pred}")
            if isinstance(lightgbm_pred, dict) and 'outputs' in lightgbm_pred:
                outputs = lightgbm_pred['outputs']
                signal_prob = outputs.get('signal_probability', 0.5)
                signal_strength = outputs.get('signal_strength', 0.5)
                confidence_score = outputs.get('confidence_score', 0.5)
                direction = outputs.get('direction_classification', 'HOLD')
                model_confidence = lightgbm_pred.get('confidence', 0.0) 
            else:
                signal_prob = lightgbm_pred.get('signal_probability', 0.5)
                signal_strength = lightgbm_pred.get('signal_strength', 0.5)
                confidence_score = lightgbm_pred.get('confidence_score', 0.5)
                direction = lightgbm_pred.get('direction_classification', 'HOLD')
                model_confidence = lightgbm_pred.get('confidence', 0.0)   

            # Final confidence to use in decisions
            signal_confidence = max(0.0, min(1.0, model_confidence))

            # Log signal analysis
            self.logger.info(f"     LightGBM Signal Analysis:")
            self.logger.info(f"       Signal Probability: {signal_prob:.3f}")
            self.logger.info(f"       Signal Strength: {signal_strength:.3f}")
            self.logger.info(f"       Confidence Score: {confidence_score:.3f}")
            self.logger.info(f"       Direction: {direction}")
            self.logger.info(f"       Model Confidence: {signal_confidence:.3f}")
            self.logger.info(f"       Minimum Threshold: {self.min_confidence_threshold:.3f}")

            # Signal quality checks
            veto_reasons = []

            # 🔧 FIXED: Correct confidence threshold logic
            self.logger.info(f"       Confidence Check: {signal_confidence:.3f} vs threshold {self.min_confidence_threshold:.3f}")
            if signal_confidence < self.min_confidence_threshold:  # FIXED: Removed backwards logic
                reason = f"Signal confidence {signal_confidence:.3f} below threshold {self.min_confidence_threshold:.3f}"
                veto_reasons.append(reason)
                self.logger.info(f"       ❌ FAILED: {reason}")
            else:
                self.logger.info(f"       ✅ PASSED: Confidence above threshold")

                # Check signal consistency with previous levels - FIXED: More reasonable thresholds
                self.logger.info(f"       Level Consistency Check:")
                self.logger.info(f"         Level 1 Confidence: {level1.confidence:.3f}")
                self.logger.info(f"         Level 2 Confidence: {level2.confidence:.3f}")

                # 🔧 FIXED: More reasonable Level 1 threshold (0.4 -> 0.3)
                if level1.confidence < 0.3:
                    reason = f"L1 veto: Very weak safety signal (L1: {level1.confidence:.3f}) despite confidence {signal_confidence:.3f}"
                    veto_reasons.append(reason)
                    self.logger.info(f"         ❌ FAILED: {reason}")

                # 🔧 FIXED: More reasonable Level 2 threshold (0.5 -> 0.35)
                if level2.confidence < 0.35:
                    reason = f"L2 veto: Very unfavorable market context (L2: {level2.confidence:.3f}) despite confidence {signal_confidence:.3f}"
                    veto_reasons.append(reason)
                    self.logger.info(f"         ❌ FAILED: {reason}")

                if not veto_reasons:
                    self.logger.info(f"         ✅ PASSED: Signal consistency checks")


            # Check for signal divergence (if available)
            feature_importance = lightgbm_pred.get('feature_importance', {})
            if feature_importance:
                top_features = sorted(feature_importance.items(), key=lambda x: x[1], reverse=True)[:3]
                if all(importance < 0.1 for _, importance in top_features):
                    veto_reasons.append("Weak feature importance in signal generation")

            # Determine decision and direction
            if veto_reasons:
                level.decision = "VETO"
                level.veto_reason = "; ".join(veto_reasons)
                # keep original signal_confidence for transparency
                level.confidence = signal_confidence
                level.reasoning = f"Signal quality veto: {level.veto_reason}"
            else:
                # 🔧 FIXED: Proper direction logic based on signal probability and direction classification
                if direction in ['BUY', 'SELL']:
                    # Use model's direction classification if available
                    level.decision = direction
                elif signal_prob > 0.5 + (self.min_confidence_threshold - 0.5) / 2:  # Symmetric threshold above 0.5
                    level.decision = "BUY"
                elif signal_prob < 0.5 - (self.min_confidence_threshold - 0.5) / 2:  # Symmetric threshold below 0.5
                    level.decision = "SELL"
                else:
                    level.decision = "HOLD"  # Neutral zone - perfectly balanced

                level.confidence = signal_confidence
                level.reasoning = f"Signal quality good (prob: {signal_prob:.3f}, conf: {signal_confidence:.3f}, direction: {level.decision})"

            return level

        except Exception as e:
            level.decision = "VETO"
            level.veto_reason = f"Signal quality error: {str(e)}"
            # keep as None to indicate error, not "0.0 confidence"
            level.confidence = None
            level.reasoning = level.veto_reason
            return level

    def _level4_final_integration(self, xgboost_pred: Dict[str, Any],
                                 market_context: Dict[str, Any],
                                 level1: DecisionLevel,
                                 level2: DecisionLevel,
                                 level3: DecisionLevel) -> DecisionLevel:
        """
        Level 4: Final Decision Integration using XGBoost Risk Manager.

        Integrates all previous levels and applies final risk adjustments.
        """
        level = DecisionLevel(
            level=4,
            name="Final Decision Integration",
            model_type="xgboost",
            weight=0.25,
            veto_power=False  # Final level adjusts but doesn't veto
        )

        try:
            # Extract XGBoost risk analysis
            risk_score = xgboost_pred.get('risk_score', 0.5)
            position_size_adj = xgboost_pred.get('position_size_adjustment', 1.0)
            print(f"level3.decision: {level3.decision}")
            # Integrate previous level decisions
            if level3.decision in ['BUY', 'SELL']:
                level.decision = level3.decision
            else:
                level.decision = "HOLD"

            # Calculate integrated confidence
            weighted_confidence = (
                level1.confidence * level1.weight +
                level2.confidence * level2.weight +
                level3.confidence * level3.weight
            ) / (level1.weight + level2.weight + level3.weight)

            # Apply risk adjustments
            risk_adjusted_confidence = weighted_confidence * (1 - risk_score * 0.5)

            level.confidence = max(0.0, min(1.0, risk_adjusted_confidence))
            level.reasoning = f"Risk-adjusted integration (risk: {risk_score:.3f}, pos_adj: {position_size_adj:.3f})"

            return level

        except Exception as e:
            level.decision = "HOLD"
            level.confidence = 0.0
            level.reasoning = f"Final integration error: {str(e)}"
            return level

    def _integrate_decisions(self, decision_levels: Dict[int, DecisionLevel],
                           model_predictions: Dict[str, Any],
                           market_context: Dict[str, Any]) -> HierarchicalDecision:
        """Integrate all decision levels into final hierarchical decision."""

        # Get final decision from Level 4
        final_level = decision_levels[4]
        final_decision = final_level.decision

        # Calculate overall confidence (weighted average)
        total_weight = sum(level.weight for level in decision_levels.values())
        overall_confidence = sum(
            level.confidence * level.weight for level in decision_levels.values()
        ) / total_weight if total_weight > 0 else 0.0

        # Calculate consensus score
        decisions = [level.decision for level in decision_levels.values() if level.decision != "VETO"]
        if len(decisions) > 1:
            consensus_score = len([d for d in decisions if d == final_decision]) / len(decisions)
        else:
            consensus_score = 1.0 if decisions else 0.0

        # Calculate risk score
        risk_score = 1.0 - overall_confidence

        # Determine entry timing
        if overall_confidence >= self.high_confidence_threshold:
            entry_timing = "IMMEDIATE"
        elif overall_confidence >= self.min_confidence_threshold:
            entry_timing = "PATIENT"
        else:
            entry_timing = "WAIT"

        # Calculate position sizing
        position_sizing = self._calculate_position_sizing(decision_levels, overall_confidence)

        # Calculate TP/SL levels
        tp_sl_levels = self._calculate_tp_sl_levels(decision_levels, model_predictions)

        # Log final hierarchical decision with comprehensive summary
        self.logger.info("=" * 60)
        self.logger.info("🎯 FINAL HIERARCHICAL DECISION SUMMARY")
        self.logger.info(f"   Decision: {final_decision}")
        self.logger.info(f"   Overall Confidence: {overall_confidence:.3f}")
        self.logger.info(f"   Consensus Score: {consensus_score:.3f}")
        self.logger.info(f"   Risk Score: {risk_score:.3f}")
        self.logger.info(f"   Position Sizing: {position_sizing:.3f}")
        self.logger.info(f"   Entry Timing: {entry_timing}")

        # Log TP/SL levels
        if tp_sl_levels:
            self.logger.info("   TP/SL Levels:")
            for level, value in tp_sl_levels.items():
                self.logger.info(f"     {level}: {value}")

        # Log level-by-level summary
        self.logger.info("   Level Summary:")
        for level_num, level_result in decision_levels.items():
            status = "✅ PASSED" if not level_result.veto_reason else f"❌ VETOED: {level_result.veto_reason}"
            self.logger.info(f"     Level {level_num} ({level_result.name}): {status} | Confidence: {level_result.confidence:.3f}")

        self.logger.info("=" * 60)

        return HierarchicalDecision(
            final_decision=final_decision,
            overall_confidence=overall_confidence,
            decision_levels=decision_levels,
            consensus_score=consensus_score,
            risk_score=risk_score,
            entry_timing=entry_timing,
            position_sizing=position_sizing,
            tp_sl_levels=tp_sl_levels,
            timestamp=datetime.now()
        )

    def _calculate_position_sizing(self, decision_levels: Dict[int, DecisionLevel],
                                 overall_confidence: float) -> float:
        """Calculate position sizing based on hierarchical decision."""
        base_size = 1.0

        # Confidence-based adjustment
        confidence_multiplier = 0.5 + (overall_confidence * 1.5)  # 0.5 to 2.0 range

        # Level-specific adjustments
        safety_multiplier = max(0.5, decision_levels[1].confidence)  # Safety check
        regime_multiplier = max(0.7, decision_levels[2].confidence)  # Market context
        signal_multiplier = max(0.8, decision_levels[3].confidence)  # Signal quality

        final_size = base_size * confidence_multiplier * safety_multiplier * regime_multiplier * signal_multiplier

        return max(0.1, min(2.0, final_size))  # Clamp between 0.1x and 2.0x

    def _calculate_tp_sl_levels(self, decision_levels: Dict[int, DecisionLevel],
                              model_predictions: Dict[str, Any]) -> Dict[str, float]:
        """
        CRITICAL FIX: Use ACTUAL XGBoost model outputs for TP/SL levels.
        This method now extracts the actual model predictions instead of using complex calculations.
        """

        self.logger.info("🔍 HIERARCHICAL DECISION: EXTRACTING ACTUAL MODEL TP/SL LEVELS")

        # PRIORITY 1: Use actual XGBoost TP/SL predictions if available
        xgboost_pred = model_predictions.get('xgboost', {})
        xgboost_outputs = xgboost_pred.get('outputs', {})

        actual_sl_pips = xgboost_outputs.get('stop_loss_pips', None)
        actual_tp_levels = xgboost_outputs.get('take_profit_levels', None)

        if actual_sl_pips is not None and actual_tp_levels is not None and len(actual_tp_levels) >= 3:
            # Use actual XGBoost model outputs
            tp1_distance = actual_tp_levels[0]
            tp2_distance = actual_tp_levels[1]
            tp3_distance = actual_tp_levels[2]
            sl_distance = actual_sl_pips

            self.logger.info(f"   ✅ Using ACTUAL XGBoost TP/SL predictions:")
            self.logger.info(f"      SL: {sl_distance:.2f} pips")
            self.logger.info(f"      TP levels: {actual_tp_levels}")

            return {
                'stop_loss_pips': sl_distance,
                'tp1_pips': tp1_distance,
                'tp2_pips': tp2_distance,
                'tp3_pips': tp3_distance,
                'tp1_distance': tp1_distance,  # For backward compatibility
                'tp2_distance': tp2_distance,  # For backward compatibility
                'sl_distance': sl_distance,    # For backward compatibility
                'tp1_percentage': 0.4,  # 40% of position
                'tp2_percentage': 0.35,  # 35% of position
                'trailing_percentage': 0.25  # 25% for trailing
            }

        # FALLBACK: Only if XGBoost predictions are not available
        self.logger.warning("   ❌ XGBoost TP/SL predictions not found, using fallback calculation")

        # Get base levels from LightGBM (primary signal generator)
        lightgbm_pred = model_predictions.get('lightgbm', {})
        lightgbm_outputs = lightgbm_pred.get('outputs', {})
        base_tp1 = lightgbm_outputs.get('tp1_distance', 15.0)
        base_tp2 = lightgbm_outputs.get('tp2_distance', 30.0)
        base_sl = lightgbm_outputs.get('sl_distance', 20.0)

        # Apply volatility adjustment from Linear model
        linear_pred = model_predictions.get('linear', {})
        linear_outputs = linear_pred.get('outputs', {})
        vol_adjustment = linear_outputs.get('volatility_adjustment', 1.0)

        # Calculate fallback levels
        tp1_distance = base_tp1 * vol_adjustment
        tp2_distance = base_tp2 * vol_adjustment
        tp3_distance = tp2_distance * 1.5  # Estimate TP3
        sl_distance = base_sl * vol_adjustment

        self.logger.info(f"   ⚠️  Using fallback TP/SL calculation:")
        self.logger.info(f"      SL: {sl_distance:.2f} pips (fallback)")
        self.logger.info(f"      TP1: {tp1_distance:.2f} pips (fallback)")
        self.logger.info(f"      TP2: {tp2_distance:.2f} pips (fallback)")
        self.logger.info(f"      TP3: {tp3_distance:.2f} pips (fallback)")

        return {
            'stop_loss_pips': max(8.0, sl_distance),
            'tp1_pips': max(5.0, tp1_distance),
            'tp2_pips': max(10.0, tp2_distance),
            'tp3_pips': max(15.0, tp3_distance),
            'tp1_distance': max(5.0, tp1_distance),  # For backward compatibility
            'tp2_distance': max(10.0, tp2_distance),  # For backward compatibility
            'sl_distance': max(8.0, sl_distance),    # For backward compatibility
            'tp1_percentage': 0.4,  # 40% of position
            'tp2_percentage': 0.35,  # 35% of position
            'trailing_percentage': 0.25  # 25% for trailing
        }

    def _create_veto_decision(self, veto_level: DecisionLevel,
                            decision_levels: Dict[int, DecisionLevel],
                            timestamp: datetime) -> HierarchicalDecision:
        """Create a veto decision when a level vetoes the trade."""

        # Log comprehensive veto summary
        self.logger.info("=" * 60)
        self.logger.info("🚫 HIERARCHICAL DECISION VETOED")
        self.logger.info(f"   Vetoed by: {veto_level.name}")
        self.logger.info(f"   Veto Reason: {veto_level.veto_reason}")
        self.logger.info(f"   Veto Level Confidence: {veto_level.confidence:.3f}")

        # Show which levels passed before the veto
        self.logger.info("   Level Status:")
        for level_num, level_result in decision_levels.items():
            if level_result.veto_reason:
                self.logger.info(f"     Level {level_num} ({level_result.name}): ❌ VETOED - {level_result.veto_reason}")
                break
            else:
                self.logger.info(f"     Level {level_num} ({level_result.name}): ✅ PASSED - {level_result.reasoning}")

        self.logger.info("   Final Decision: VETO (No trade executed)")
        self.logger.info("=" * 60)

        return HierarchicalDecision(
            final_decision="VETO",
            overall_confidence=0.0,
            decision_levels=decision_levels,
            consensus_score=0.0,
            risk_score=1.0,
            entry_timing="WAIT",
            position_sizing=0.0,
            tp_sl_levels={},
            timestamp=timestamp
        )

    def _create_error_decision(self, error_msg: str, timestamp: datetime) -> HierarchicalDecision:
        """Create an error decision when the framework fails."""
        return HierarchicalDecision(
            final_decision="ERROR",
            overall_confidence=0.0,
            decision_levels={},
            consensus_score=0.0,
            risk_score=1.0,
            entry_timing="WAIT",
            position_sizing=0.0,
            tp_sl_levels={},
            timestamp=timestamp
        )

    def _extract_ensemble_data(self, model_predictions: Dict[str, Any]) -> Dict[str, Any]:
        """
        Extract ensemble prediction data from model predictions.

        NEW: Helper method to extract ensemble data.
        """
        try:
            # Get the first (and likely only) prediction
            prediction_key = list(model_predictions.keys())[0]
            prediction = model_predictions[prediction_key]

            if isinstance(prediction, dict):
                # Extract ensemble components
                predictions = prediction.get('predictions', {})
                analysis = prediction.get('analysis', {})
                trading_signal = prediction.get('trading_signal', {})
                metadata = prediction.get('metadata', {})

                return {
                    'predictions': predictions,
                    'analysis': analysis,
                    'trading_signal': trading_signal,
                    'metadata': metadata,
                    'source_key': prediction_key
                }
            else:
                self.logger.warning("⚠️ Unexpected prediction format for ensemble extraction")
                return {}

        except Exception as e:
            self.logger.error(f"❌ Failed to extract ensemble data: {str(e)}")
            return {}

    def _log_ensemble_data(self, ensemble_data: Dict[str, Any]):
        """
        Log ensemble data for debugging.

        NEW: Helper method to log ensemble data.
        """
        try:
            predictions = ensemble_data.get('predictions', {})
            analysis = ensemble_data.get('analysis', {})
            trading_signal = ensemble_data.get('trading_signal', {})

            self.logger.info("📊 ENSEMBLE PREDICTION DATA:")
            self.logger.info(f"   Signal Probability: {predictions.get('signal_probability', 0.0):.3f}")
            self.logger.info(f"   TP1 Distance: {predictions.get('tp1_distance', 0.0):.1f}")
            self.logger.info(f"   TP2 Distance: {predictions.get('tp2_distance', 0.0):.1f}")
            self.logger.info(f"   SL Distance: {predictions.get('sl_distance', 0.0):.1f}")
            self.logger.info(f"   Market Regime: {predictions.get('market_regime', 0.0):.3f}")
            self.logger.info(f"   Volatility Adjustment: {predictions.get('volatility_adjustment', 1.0):.2f}")

            self.logger.info("📊 ENSEMBLE ANALYSIS:")
            self.logger.info(f"   Confidence Level: {analysis.get('confidence_level', 'unknown')}")
            self.logger.info(f"   Should Trade: {analysis.get('should_trade', False)}")
            self.logger.info(f"   High Confidence: {analysis.get('high_confidence', False)}")

            self.logger.info("📊 ENSEMBLE TRADING SIGNAL:")
            self.logger.info(f"   Should Enter: {trading_signal.get('should_enter', False)}")
            self.logger.info(f"   Direction: {trading_signal.get('direction', 'unknown')}")
            self.logger.info(f"   Confidence: {trading_signal.get('confidence', 0.0):.3f}")

        except Exception as e:
            self.logger.warning(f"⚠️ Failed to log ensemble data: {str(e)}")

    def _create_ensemble_decision_levels(self, ensemble_data: Dict[str, Any],
                                       market_context: Dict[str, Any]) -> Dict[int, DecisionLevel]:
        """
        Create decision levels based on ensemble data.

        NEW: Create decision levels from ensemble predictions.
        """
        try:
            predictions = ensemble_data.get('predictions', {})
            analysis = ensemble_data.get('analysis', {})
            trading_signal = ensemble_data.get('trading_signal', {})

            decision_levels = {}

            # Level 1: Safety Check (based on ensemble system health)
            level1 = DecisionLevel(
                level=1,
                name="Safety Check",
                model_type="ensemble_safety",
                weight=0.10,
                veto_power=True
            )

            # Use ensemble metadata for safety assessment
            metadata = ensemble_data.get('metadata', {})
            model_count = metadata.get('model_count', 1)
            feature_count = metadata.get('feature_count', 0)

            if model_count >= 2 and feature_count >= 300:  # Reasonable thresholds
                level1.decision = "PROCEED"
                level1.confidence = 0.8
                level1.reasoning = f"Ensemble safety check passed (models: {model_count}, features: {feature_count})"
            else:
                level1.decision = "VETO"
                level1.veto_reason = f"Insufficient ensemble components (models: {model_count}, features: {feature_count})"
                level1.confidence = 0.0
                level1.reasoning = level1.veto_reason

            decision_levels[1] = level1

            # Level 2: Market Context Analysis (based on ensemble market regime analysis)
            level2 = DecisionLevel(
                level=2,
                name="Market Context Analysis",
                model_type="ensemble_regime",
                weight=0.25,
                veto_power=True
            )

            market_regime = predictions.get('market_regime', 0.5)
            volatility_adjustment = predictions.get('volatility_adjustment', 1.0)

            # Market regime suitability check
            if market_regime > 0.3 and volatility_adjustment < 2.0:  # Reasonable market conditions
                level2.decision = "PROCEED"
                level2.confidence = min(1.0, market_regime + 0.2)
                level2.reasoning = f"Market conditions suitable (regime: {market_regime:.3f}, vol_adj: {volatility_adjustment:.2f})"
            else:
                level2.decision = "VETO"
                level2.veto_reason = f"Unfavorable market conditions (regime: {market_regime:.3f}, vol_adj: {volatility_adjustment:.2f})"
                level2.confidence = 0.0
                level2.reasoning = level2.veto_reason

            decision_levels[2] = level2

            # Level 3: Signal Quality Assessment (based on ensemble signal analysis)
            level3 = DecisionLevel(
                level=3,
                name="Signal Quality Assessment",
                model_type="ensemble_signal",
                weight=0.40,
                veto_power=True
            )

            signal_probability = predictions.get('signal_probability', 0.5)
            should_trade = analysis.get('should_trade', False)

            if should_trade and signal_probability >= self.min_confidence_threshold:
                if signal_probability > 0.5:
                    level3.decision = "BUY"
                else:
                    level3.decision = "SELL"
                level3.confidence = signal_probability
                level3.reasoning = f"Signal quality acceptable (prob: {signal_probability:.3f}, should_trade: {should_trade})"
            else:
                level3.decision = "VETO"
                level3.veto_reason = f"Signal quality insufficient (prob: {signal_probability:.3f}, should_trade: {should_trade})"
                level3.confidence = signal_probability
                level3.reasoning = level3.veto_reason

            decision_levels[3] = level3

            return decision_levels

        except Exception as e:
            self.logger.error(f"❌ Failed to create ensemble decision levels: {str(e)}")
            return {}

    def _integrate_ensemble_decisions(self, decision_levels: Dict[int, DecisionLevel],
                                    ensemble_data: Dict[str, Any],
                                    market_context: Dict[str, Any]) -> HierarchicalDecision:
        """
        Integrate ensemble decision levels into final hierarchical decision.

        NEW: Integration method for ensemble decisions.
        """
        try:
            # Get final decision from Level 3 (since we only created 3 levels)
            final_level = decision_levels.get(3)
            final_decision = final_level.decision if final_level else "HOLD"

            # Calculate overall confidence (weighted average)
            total_weight = sum(level.weight for level in decision_levels.values())
            overall_confidence = sum(
                level.confidence * level.weight for level in decision_levels.values()
            ) / total_weight if total_weight > 0 else 0.0

            # Calculate consensus score
            decisions = [level.decision for level in decision_levels.values() if level.decision != "VETO"]
            if len(decisions) > 1:
                consensus_score = len([d for d in decisions if d == final_decision]) / len(decisions)
            else:
                consensus_score = 1.0 if decisions else 0.0

            # Calculate risk score
            risk_score = 1.0 - overall_confidence

            # Determine entry timing
            if overall_confidence >= self.high_confidence_threshold:
                entry_timing = "IMMEDIATE"
            elif overall_confidence >= self.min_confidence_threshold:
                entry_timing = "PATIENT"
            else:
                entry_timing = "WAIT"

            # Calculate position sizing from ensemble data
            trading_signal = ensemble_data.get('trading_signal', {})
            position_sizing = trading_signal.get('position_size_multiplier', 1.0) * overall_confidence

            # Calculate TP/SL levels from ensemble predictions
            predictions = ensemble_data.get('predictions', {})
            tp_sl_levels = {
                'tp1_distance': predictions.get('tp1_distance', 15.0),
                'tp2_distance': predictions.get('tp2_distance', 30.0),
                'sl_distance': predictions.get('sl_distance', 20.0)
            }

            # Log final ensemble hierarchical decision
            self.logger.info("=" * 60)
            self.logger.info("🎯 FINAL ENSEMBLE HIERARCHICAL DECISION SUMMARY")
            self.logger.info(f"   Decision: {final_decision}")
            self.logger.info(f"   Overall Confidence: {overall_confidence:.3f}")
            self.logger.info(f"   Consensus Score: {consensus_score:.3f}")
            self.logger.info(f"   Risk Score: {risk_score:.3f}")
            self.logger.info(f"   Position Sizing: {position_sizing:.3f}")
            self.logger.info(f"   Entry Timing: {entry_timing}")
            self.logger.info(f"   TP/SL Levels: TP1={tp_sl_levels['tp1_distance']:.1f}, TP2={tp_sl_levels['tp2_distance']:.1f}, SL={tp_sl_levels['sl_distance']:.1f}")
            self.logger.info("=" * 60)

            return HierarchicalDecision(
                final_decision=final_decision,
                overall_confidence=overall_confidence,
                decision_levels=decision_levels,
                consensus_score=consensus_score,
                risk_score=risk_score,
                entry_timing=entry_timing,
                position_sizing=position_sizing,
                tp_sl_levels=tp_sl_levels,
                timestamp=datetime.now()
            )

        except Exception as e:
            self.logger.error(f"❌ Failed to integrate ensemble decisions: {str(e)}")
            return self._create_error_decision(str(e), datetime.now())

    def _create_error_decision(self, error_message: str, timestamp: datetime) -> HierarchicalDecision:
        """
        Create error decision for exception handling.

        NEW: Helper method to create error decisions.
        """
        return HierarchicalDecision(
            final_decision="HOLD",
            overall_confidence=0.0,
            decision_levels={},
            consensus_score=0.0,
            risk_score=1.0,
            entry_timing="WAIT",
            position_sizing=0.0,
            tp_sl_levels={},
            timestamp=timestamp
        )
