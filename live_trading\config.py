"""
Live Trading Configuration

Configuration settings for live trading system including risk management,
execution parameters, and model-driven trading logic.
"""

from dataclasses import dataclass, field
from typing import Dict, Any, List, Optional
from pathlib import Path
import yaml


@dataclass
class LiveTradingConfig:
    """Configuration for live trading system."""
    
    # Trading Parameters
    symbol: str = "XAUUSD!"
    timeframe_minutes: int = 5
    prediction_interval_seconds: int = 30  # Model prediction frequency
    execution_interval_seconds: int = 60   # Trade execution check frequency

    # Simulation Parameters (for simulation mode)
    simulation_speed: int = 10             # Simulation speed multiplier
    historical_data_path: str = "data/cleaned/ohlcv_aligned.csv"  # Historical OHLCV data
    features_data_path: str = "data/cleaned/features_aligned.csv"  # Features data
    
    # Risk Management
    initial_capital: float = 100000.0      # Fallback only - real balance retrieved from MT5
    max_risk_per_trade: float = 0.04       # 4% risk per trade (doubled for multi-tier system)
    max_concurrent_trades: int = 3
    max_daily_trades: int = 20
    max_daily_loss: float = 0.05           # 5% max daily loss
    
    # Model-Driven Parameters
    min_signal_confidence: float = 0.65    # Minimum confidence for trade entry
    high_confidence_threshold: float = 0.8 # High confidence threshold
    ensemble_consensus_threshold: float = 0.7  # Minimum ensemble agreement
    
    # Position Sizing (Model-Driven)
    base_position_size: float = 0.1        # Base lot size
    confidence_multiplier: float = 1.5     # Multiply by confidence
    volatility_adjustment: bool = True     # Use model volatility adjustment
    
    # Entry/Exit Optimization (Model-Driven)
    use_model_entry_timing: bool = True    # Use model for optimal entry timing
    use_model_exit_levels: bool = True     # Use model for TP/SL levels
    entry_patience_seconds: int = 300      # Max wait time for optimal entry
    
    # Multi-Tier TP/SL System
    tp1_percentage: float = 0.4            # 40% of position for quick profits
    tp2_percentage: float = 0.35           # 35% for swing profits  
    tp3_percentage: float = 0.25           # 25% for trend following
    
    # Transaction Costs (MT5 Realistic)
    spread_pips: float = 0.18              # Average spread from analysis
    slippage_pips: float = 0.05            # Expected slippage
    commission_per_lot: float = 0.0        # Commission per lot

    # Paper Trading Simulation Parameters
    simulate_slippage: bool = False        # Simulate realistic slippage
    simulate_spread: bool = False          # Simulate realistic spreads
    simulate_latency: bool = False         # Simulate execution latency
    execution_latency_ms: int = 50         # Simulated execution latency
    
    # MT5 Connection
    mt5_timeout_seconds: int = 30
    mt5_retry_attempts: int = 3
    mt5_retry_delay: float = 1.0
    
    # Data and Models - Updated for Specialized Ensemble
    model_path: str = "data/models/retrained_lightgbm_signal_generator_20250922_001127.pkl"  # Retrained model with dynamic labels
    feature_pipeline_path: str = "data/models/xauusd_feature_pipeline.joblib"
    data_buffer_size: int = 1000           # Historical data buffer
    use_specialized_ensemble: bool = True  # Use specialized ensemble architecture

    # NEW: Ensemble Configuration Parameters
    ensemble_models_dir: str = "data/models"  # Directory containing all ensemble models
    ensemble_model_files: Dict[str, str] = field(default_factory=lambda: {
        'lightgbm': "retrained_lightgbm_signal_generator_20250922_001127.pkl",
        'catboost': "fixed_catboost_market_regime_analyst_20250921_044615.pkl",
        'xgboost': "fixed_xgboost_risk_manager_20250921_044032.pkl",
        'linear': "specialized_linear_stability_monitor_20250921_042620.pkl"
    })
    ensemble_weights: Dict[str, float] = field(default_factory=lambda: {
        'lightgbm': 0.40,  # Signal Generator
        'catboost': 0.25,  # Market Regime Analyst
        'xgboost': 0.25,   # Risk Manager
        'linear': 0.10     # Stability Monitor
    })
    
    # Performance Monitoring
    latency_warning_ms: float = 50.0       # Warn if latency > 50ms
    latency_critical_ms: float = 100.0     # Critical if latency > 100ms
    performance_log_interval: int = 300    # Log performance every 5 minutes
    
    # Session-Based Trading
    trading_sessions: Dict[str, Dict[str, Any]] = field(default_factory=lambda: {
        'asian': {
            'start_hour': 0, 'end_hour': 8,
            'position_multiplier': 0.8,     # Reduce position size
            'min_confidence': 0.7           # Higher confidence required
        },
        'european': {
            'start_hour': 8, 'end_hour': 16,
            'position_multiplier': 1.0,     # Standard position size
            'min_confidence': 0.65          # Standard confidence
        },
        'us': {
            'start_hour': 16, 'end_hour': 24,
            'position_multiplier': 1.2,     # Increase position size
            'min_confidence': 0.6           # Lower confidence acceptable
        }
    })
    
    # Emergency Controls
    emergency_stop_enabled: bool = True
    max_consecutive_losses: int = 5
    emergency_stop_loss_pct: float = 0.1   # 10% portfolio loss triggers stop
    
    # Logging and Monitoring
    log_level: str = "INFO"
    log_trades: bool = True
    log_predictions: bool = True
    log_performance: bool = True
    save_trade_history: bool = True
    
    def validate_ensemble_models(self) -> Dict[str, Any]:
        """
        Validate ensemble model availability and return status.

        Returns:
            Dict containing validation results and available models
        """
        validation_result = {
            'all_models_available': True,
            'available_models': [],
            'missing_models': [],
            'can_run_ensemble': False,
            'can_run_single_model': False,
            'warnings': []
        }

        # Check if ensemble models directory exists
        ensemble_dir = Path(self.ensemble_models_dir)
        if not ensemble_dir.exists():
            validation_result['warnings'].append(f"Ensemble models directory not found: {self.ensemble_models_dir}")
            validation_result['all_models_available'] = False

        # Check each model file
        for model_name, model_file in self.ensemble_model_files.items():
            model_path = ensemble_dir / model_file
            if model_path.exists():
                validation_result['available_models'].append(model_name)
            else:
                validation_result['missing_models'].append(model_name)
                validation_result['all_models_available'] = False
                validation_result['warnings'].append(f"Model file not found: {model_file}")

        # Determine what can run
        validation_result['can_run_single_model'] = 'lightgbm' in validation_result['available_models']
        validation_result['can_run_ensemble'] = len(validation_result['available_models']) >= 2

        return validation_result

    @classmethod
    def from_yaml(cls, config_path: str) -> 'LiveTradingConfig':
        """Load configuration from YAML file."""
        with open(config_path, 'r') as f:
            config_data = yaml.safe_load(f)

        # Support both nested and flat YAML structures
        if 'live_trading' in config_data:
            # Nested structure: live_trading: { ... }
            return cls(**config_data['live_trading'])
        else:
            # Flat structure: parameters at root level
            return cls(**config_data)
    
    def to_yaml(self, config_path: str):
        """Save configuration to YAML file."""
        config_data = {'live_trading': self.__dict__}
        with open(config_path, 'w') as f:
            yaml.dump(config_data, f, default_flow_style=False, indent=2)
    
    def get_session_config(self, current_hour: int) -> Dict[str, Any]:
        """Get trading session configuration for current hour."""
        for session_name, session_config in self.trading_sessions.items():
            start_hour = session_config['start_hour']
            end_hour = session_config['end_hour']
            
            if start_hour <= current_hour < end_hour:
                return {
                    'session_name': session_name,
                    **session_config
                }
        
        # Default to European session if no match
        return {
            'session_name': 'european',
            **self.trading_sessions['european']
        }
    
    def get_risk_adjusted_position_size(self, account_balance: float, 
                                      sl_distance_pips: float,
                                      confidence: float = 0.7) -> float:
        """Calculate risk-adjusted position size."""
        # Base risk amount
        risk_amount = account_balance * self.max_risk_per_trade
        
        # Calculate position size based on stop loss distance
        pip_value = 1.0  # For XAUUSD, 1 pip = $1 per 0.01 lot
        position_size = risk_amount / (sl_distance_pips * pip_value)
        
        # Apply confidence multiplier
        if confidence > self.high_confidence_threshold:
            position_size *= self.confidence_multiplier
        
        # Ensure minimum and maximum limits
        position_size = max(0.01, min(position_size, 10.0))  # 0.01 to 10 lots
        
        return round(position_size, 2)
    
    def validate(self) -> List[str]:
        """Validate configuration and return list of issues."""
        issues = []
        
        if self.max_risk_per_trade <= 0 or self.max_risk_per_trade > 0.1:
            issues.append("max_risk_per_trade should be between 0 and 0.1 (10%) - current multi-tier system uses 4%")
        
        if self.min_signal_confidence < 0.5 or self.min_signal_confidence > 1.0:
            issues.append("min_signal_confidence should be between 0.5 and 1.0")
        
        if not Path(self.model_path).exists():
            issues.append(f"Model path does not exist: {self.model_path}")
        
        if self.prediction_interval_seconds < 10:
            issues.append("prediction_interval_seconds should be at least 10 seconds")
        
        return issues

    def get(self, key: str, default=None):
        """Get configuration value by key with optional default."""
        return getattr(self, key, default)

    def __getitem__(self, key: str):
        """Allow dictionary-style access."""
        return getattr(self, key)

    def __setitem__(self, key: str, value):
        """Allow dictionary-style assignment."""
        setattr(self, key, value)

    def __contains__(self, key: str):
        """Check if key exists in configuration."""
        return hasattr(self, key)

    def keys(self):
        """Return configuration keys."""
        return [attr for attr in dir(self) if not attr.startswith('_') and not callable(getattr(self, attr))]

    def items(self):
        """Return configuration items."""
        return [(key, getattr(self, key)) for key in self.keys()]
